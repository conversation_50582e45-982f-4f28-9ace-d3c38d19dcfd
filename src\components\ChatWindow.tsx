import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  TabsContent,
  Typography
} from "dbh-reactui";
import React, { useState } from "react";
import ChatRemoveIcon from "../assets/delete-bin-line.svg?react";
import ChatMenuIcon from "../assets/more-2-fill.svg?react";
import { useChatMessageContext } from "../context/chat-messages-context.ts";
import { useChatMutations } from "../hooks/UseChatMutations.ts";
import useKiBotAskMutation from "../hooks/UseKiBotAskMutation.ts";
import { DeleteConversationProps, User } from "../types/api.ts";
import Chat from "./Chat.tsx";
import ChatInput from "./ChatInput.tsx";

type ChatWindowProps = {
  productId: string;
  name: string
  user: User;
  conversationId: string
};

const ChatWindow: React.FC<ChatWindowProps> = ({ productId, name, user, conversationId }) => {
  const [question, setQuestion] = useState<string>("");
  const { mutation } = useKiBotAskMutation(productId, user, name, conversationId);

  const { savedChatMessages } = useChatMessageContext();
  const {deleteOrClearChatMutation} = useChatMutations({dataId: undefined, productId, conversationId, name})

  
  const handleQuestionInputClick = () => {
    if (!question) return;

   
    mutation.mutate({ question, productId });
    setQuestion("");
  };

  const handleCommentSubmit = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (question.trim()) {
        mutation.mutate({ question, productId });
        setQuestion("");
      }
    }
  };

  const handleDeleteOrClearConversation = (clearOnly: boolean)=>{
     const deleteChatProps: DeleteConversationProps ={
        clearOnly: clearOnly,
        conversationId: savedChatMessages[name][0].conversationId,
        userId: user.userId
     }

      deleteOrClearChatMutation.mutate(deleteChatProps)
  }


  return (
    <TabsContent
      value={conversationId}
      className={
        "relative flex h-full w-0 flex-col gap-3 data-[state=active]:flex data-[state=active]:h-[610px] data-[state=active]:max-h-[610px] data-[state=active]:w-full data-[state=active]:flex-col"
      }
    >
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant={"outlined"} className={"absolute right-0 min-h-10 min-w-10"}>
            <ChatMenuIcon className={"h-3 w-3"} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem
            className={"gap-1"}
            onClick={()=>handleDeleteOrClearConversation(false)}
          >
            <ChatRemoveIcon className={"h-4 w-4"} />
            <span>Delete Chat</span>
          </DropdownMenuItem>
          <DropdownMenuItem className={"gap-1"} onClick={()=>handleDeleteOrClearConversation(true)}>
            <ChatRemoveIcon className={"h-4 w-4"} />
            <span>Clear Chat</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Chat messages={savedChatMessages[name]} mutation={mutation}  />
      <ChatInput
        value={question}
        isPending={mutation.isPending}
        onValueChange={setQuestion}
        onSubmit={handleCommentSubmit}
        onClick={handleQuestionInputClick}
      />
      <div className="flex rounded-md ">
        <Typography variant="caption_2_strong" className="text-gray-300">
          *Die Nachrichten werden von einer KI beantwortet und können fehlerhaft sein.
        </Typography>
      </div>
    </TabsContent>
  );
};

export default ChatWindow;
