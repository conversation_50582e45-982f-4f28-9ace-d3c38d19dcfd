import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  Label,
} from "dbh-reactui";
import { useAuth } from "react-oidc-context";
import UserIcon from "../assets/user-line.svg?react";
import UserLoggedInIcon from "../assets/user-follow-line.svg?react";
import LoginIcon from "../assets/login-box-line.svg?react";
import LogoutIcon from "../assets/logout-box-line.svg?react";
import InfoLogo from "../assets/information-line.svg?react";
import { KiBotService } from "../KiBotService";
import { useEffect } from "react";

const AccountDropdown = ({ onDialogTrigger }: { onDialogTrigger: (state: boolean) => void }) => {
  const auth = useAuth();

  // 👇 Dieser Effekt läuft, wenn der Benutzer nach Login vorhanden ist
  useEffect(() => {
    if (auth.isAuthenticated && auth.user) {
      const p = auth.user.profile;
      const user = {
        userId: p.sub ?? "",
        username: p.preferred_username ?? "Guest",
        email: p.email ?? "",
        emailVerified: p.email_verified ?? false,
        name: p.name ?? "",
        firstname: p.given_name ?? "",
        lastname: p.family_name ?? "",
        language: "",
      };

      // User nur einmal anlegen/aktualisieren
      KiBotService.createOrUpdateUser(user);
    }
  }, [auth.isAuthenticated, auth.user]); // läuft, sobald Login-Status sich ändert

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outlined" className="min-h-10 min-w-10">
          {auth.user ? <UserLoggedInIcon className="h-5 w-5" /> : <UserIcon className="h-5 w-5" />}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {auth.user && (
          <DropdownMenuLabel>
            <Label>Logged in with: {auth.user.profile.preferred_username}</Label>
          </DropdownMenuLabel>
        )}

        {!auth.user ? (
          <DropdownMenuItem
            className="gap-2"
            onClick={() => {
              auth.signinRedirect(); // Redirect-only, kein await nötig
            }}
          >
            <LoginIcon className="h-5 w-5" />
            Login
          </DropdownMenuItem>
        ) : (
          <DropdownMenuItem className="gap-2" onClick={() => auth.signoutRedirect()}>
            <LogoutIcon className="h-5 w-5" />
            Logout
          </DropdownMenuItem>
        )}

        <DropdownMenuItem onClick={() => onDialogTrigger(true)}>
          <div className="flex flex-row gap-2">
            <InfoLogo className="h-5 w-5" />
            System Informations
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AccountDropdown;
